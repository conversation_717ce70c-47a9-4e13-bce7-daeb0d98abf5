{"name": "moneyprinter-frontend", "version": "1.0.0", "description": "Vue.js frontend for MoneyPrinterTurbo", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "@vueuse/core": "^10.5.0", "vue-toastification": "^2.0.0-rc.5", "lucide-vue-next": "^0.294.0", "tailwindcss": "^3.3.6", "@headlessui/vue": "^1.7.16"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}}