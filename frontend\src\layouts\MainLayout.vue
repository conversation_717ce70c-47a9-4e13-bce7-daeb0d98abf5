<template>
  <div class="min-h-screen bg-gray-50 dark:bg-dark-900">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-dark-800 shadow-sm border-b border-gray-200 dark:border-dark-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0 flex items-center">
              <Video class="h-8 w-8 text-primary-600" />
              <span class="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                MoneyPrinter Turbo
              </span>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- Credits Display -->
            <div class="flex items-center space-x-2 px-3 py-1 bg-primary-50 dark:bg-primary-900 rounded-full">
              <Coins class="h-4 w-4 text-primary-600 dark:text-primary-400" />
              <span class="text-sm font-medium text-primary-700 dark:text-primary-300">
                {{ userStore.credits }} credits
              </span>
            </div>
            
            <!-- Admin Link -->
            <router-link
              v-if="authStore.isAdmin"
              to="/admin"
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Settings class="h-5 w-5" />
            </router-link>
            
            <!-- User Menu -->
            <div class="relative">
              <button
                @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
              >
                <div class="h-8 w-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                  <User class="h-4 w-4 text-primary-600 dark:text-primary-400" />
                </div>
                <ChevronDown class="h-4 w-4" />
              </button>
              
              <!-- Dropdown Menu -->
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-50"
              >
                <div class="py-1">
                  <div class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-dark-700">
                    {{ authStore.user?.email }}
                  </div>
                  <button
                    @click="handleLogout"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700"
                  >
                    <LogOut class="inline h-4 w-4 mr-2" />
                    Sign out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
    
    <!-- Main Content -->
    <main>
      <slot />
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Video, Coins, Settings, User, ChevronDown, LogOut } from 'lucide-vue-next'
import { useAuthStore } from '@/stores/auth'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()

const showUserMenu = ref(false)

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const closeUserMenu = (event) => {
  if (!event.target.closest('.relative')) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeUserMenu)
  userStore.fetchUserData()
})

onUnmounted(() => {
  document.removeEventListener('click', closeUserMenu)
})
</script>
