import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  const initializeAuth = async () => {
    if (token.value) {
      try {
        const response = await api.get('/auth/me')
        user.value = response.data
      } catch (error) {
        logout()
      }
    }
  }

  const login = async (credentials) => {
    loading.value = true
    try {
      const response = await api.post('/auth/login', credentials)
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', token.value)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const loginWithGoogle = async (googleToken) => {
    loading.value = true
    try {
      const response = await api.post('/auth/google', { token: googleToken })
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', token.value)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
  }

  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    initializeAuth,
    login,
    loginWithGoogle,
    logout,
    updateUser
  }
})
