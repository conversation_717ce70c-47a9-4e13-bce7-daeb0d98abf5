import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  const initializeAuth = async () => {
    if (token.value) {
      try {
        // TODO: Replace with real API call when backend is ready
        // const response = await api.get('/auth/me')
        // user.value = response.data

        // Mock user data for testing
        const mockUser = JSON.parse(localStorage.getItem('mockUser'))
        if (mockUser) {
          user.value = mockUser
        } else {
          logout()
        }
      } catch (error) {
        logout()
      }
    }
  }

  const login = async (credentials) => {
    loading.value = true
    try {
      // TODO: Replace with real API call when backend is ready
      // const response = await api.post('/auth/login', credentials)

      // Mock authentication for testing
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay

      let mockUser
      if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
        mockUser = {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin'
        }
      } else if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
        mockUser = {
          id: 2,
          name: 'Regular User',
          email: '<EMAIL>',
          role: 'user'
        }
      } else {
        throw new Error('Invalid credentials')
      }

      const mockToken = 'mock-jwt-token-' + Date.now()
      token.value = mockToken
      user.value = mockUser
      localStorage.setItem('token', mockToken)
      localStorage.setItem('mockUser', JSON.stringify(mockUser))

      return { token: mockToken, user: mockUser }
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const loginWithGoogle = async () => {
    loading.value = true
    try {
      // Import Google Auth service
      const { googleAuth } = await import('@/services/auth')
      const googleResult = await googleAuth.signIn()

      // TODO: Send Google token to backend for verification
      // const response = await api.post('/auth/google', { token: googleResult.token })
      // token.value = response.data.token
      // user.value = response.data.user

      // For now, use Google user data directly (mock backend response)
      const mockToken = 'google-jwt-token-' + Date.now()
      token.value = mockToken
      user.value = {
        ...googleResult.user,
        role: googleResult.user.email.includes('admin') ? 'admin' : 'user'
      }

      localStorage.setItem('token', mockToken)
      localStorage.setItem('mockUser', JSON.stringify(user.value))

      return { token: mockToken, user: user.value }
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('mockUser')
  }

  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    initializeAuth,
    login,
    loginWithGoogle,
    logout,
    updateUser
  }
})
