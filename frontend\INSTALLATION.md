# MoneyPrinter Turbo Frontend - Installation Guide

This guide will help you set up the Vue.js frontend for MoneyPrinterTurbo.

## Prerequisites

- Node.js 18+ and npm
- MoneyPrinterTurbo backend running
- Basic knowledge of Vue.js and JavaScript

## Step 1: Install Dependencies

Navigate to the frontend directory and install dependencies:

```bash
cd frontend
npm install
```

## Step 2: Environment Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file with your configuration:
   ```env
   # Google OAuth Configuration (to be implemented later)
   VUE_APP_GOOGLE_CLIENT_ID=your_google_client_id_here

   # API Configuration
   VUE_APP_API_BASE_URL=http://localhost:8080/api

   # Application Configuration
   VUE_APP_APP_NAME=MoneyPrinter Turbo
   VUE_APP_APP_VERSION=1.0.0
   ```

## Step 3: Backend Integration

The frontend is designed to work with the existing MoneyPrinterTurbo backend. Make sure your backend is running and accessible.

### Required Backend Endpoints

The frontend expects these API endpoints to be available:

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/google` - Google OAuth login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

#### User Operations
- `GET /api/user/profile` - Get user profile
- `GET /api/user/credits` - Get user credits
- `POST /api/videos/generate` - Generate video
- `GET /api/videos/history` - Get video history

#### Admin Operations
- `GET /api/admin/stats` - Dashboard statistics
- `GET /api/admin/users` - Get all users
- `PUT /api/admin/users/:id` - Update user
- `POST /api/admin/users/:id/credits` - Add credits
- `GET /api/admin/settings` - Get settings
- `PUT /api/admin/settings` - Update settings

## Step 4: Development Server

Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Step 5: Default Admin Account

For testing purposes, you can use these mock credentials:

- **Admin Account**: <EMAIL> / password
- **User Account**: <EMAIL> / password

*Note: These are placeholder credentials. In production, implement proper authentication.*

## Step 6: Google OAuth Setup (Optional)

To enable Google OAuth authentication:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add your domain to authorized origins
6. Update `VUE_APP_GOOGLE_CLIENT_ID` in `.env`

## Step 7: Production Build

To build for production:

```bash
npm run build
```

The built files will be in the `dist/` directory.

## Step 8: Deployment

### Option 1: Static File Server

Deploy the `dist/` folder to any static file server (Nginx, Apache, etc.).

**Important**: Configure your server to redirect all routes to `index.html` for SPA routing.

### Option 2: Docker

Create a `Dockerfile`:

```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure your backend allows requests from the frontend domain
2. **API Not Found**: Verify the backend is running and `VUE_APP_API_BASE_URL` is correct
3. **Build Errors**: Clear node_modules and reinstall dependencies

### Development Tips

1. Use browser dev tools to inspect network requests
2. Check the console for JavaScript errors
3. Verify API responses match expected format

## Features Overview

### User Features
- ✅ Login page with Google OAuth placeholder
- ✅ Dashboard with credit display
- ✅ Video generation form
- ✅ Video history and downloads
- ✅ Responsive design with dark mode

### Admin Features
- ✅ Admin dashboard with statistics
- ✅ User management interface
- ✅ Credit management system
- ✅ System settings configuration
- ✅ Role-based access control

## Next Steps

1. **Implement Backend APIs**: Create the required endpoints in your backend
2. **Google OAuth**: Set up actual Google authentication
3. **Real-time Updates**: Add WebSocket support for live status updates
4. **File Upload**: Add support for custom video assets
5. **Payment Integration**: Add credit purchase functionality

## Support

For issues and questions:
1. Check the console for error messages
2. Verify backend connectivity
3. Review the API documentation
4. Check network requests in browser dev tools

## License

This frontend is part of the MoneyPrinterTurbo project and follows the same license terms.
