<template>
  <div class="p-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">System Settings</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">
        Configure system-wide settings and API keys
      </p>
    </div>
    
    <div class="max-w-4xl">
      <!-- API Configuration -->
      <div class="card p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">API Configuration</h2>
        
        <form @submit.prevent="saveSettings" class="space-y-6">
          <!-- LLM Provider -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                LLM Provider
              </label>
              <select v-model="settings.llmProvider" class="input">
                <option value="gemini">Gemini</option>
                <option value="openai">OpenAI</option>
                <option value="claude">Claude</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Language
              </label>
              <select v-model="settings.language" class="input">
                <option value="en">English</option>
                <option value="vi">Vietnamese</option>
                <option value="zh">Chinese</option>
                <option value="ja">Japanese</option>
              </select>
            </div>
          </div>
          
          <!-- API Keys -->
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Gemini API Key
                <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input
                  v-model="settings.geminiApiKey"
                  :type="showGeminiKey ? 'text' : 'password'"
                  class="input pr-10"
                  placeholder="Enter Gemini API key"
                />
                <button
                  type="button"
                  @click="showGeminiKey = !showGeminiKey"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <Eye v-if="!showGeminiKey" class="h-4 w-4 text-gray-400" />
                  <EyeOff v-else class="h-4 w-4 text-gray-400" />
                </button>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Pexels API Key
              </label>
              <div class="relative">
                <input
                  v-model="settings.pexelsApiKey"
                  :type="showPexelsKey ? 'text' : 'password'"
                  class="input pr-10"
                  placeholder="Enter Pexels API key"
                />
                <button
                  type="button"
                  @click="showPexelsKey = !showPexelsKey"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <Eye v-if="!showPexelsKey" class="h-4 w-4 text-gray-400" />
                  <EyeOff v-else class="h-4 w-4 text-gray-400" />
                </button>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Pixabay API Key
              </label>
              <div class="relative">
                <input
                  v-model="settings.pixabayApiKey"
                  :type="showPixabayKey ? 'text' : 'password'"
                  class="input pr-10"
                  placeholder="Enter Pixabay API key"
                />
                <button
                  type="button"
                  @click="showPixabayKey = !showPixabayKey"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <Eye v-if="!showPixabayKey" class="h-4 w-4 text-gray-400" />
                  <EyeOff v-else class="h-4 w-4 text-gray-400" />
                </button>
              </div>
            </div>
          </div>
          
          <!-- Model Configuration -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Base URL
              </label>
              <input
                v-model="settings.baseUrl"
                type="url"
                class="input"
                placeholder="https://api.example.com"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Model Name
              </label>
              <input
                v-model="settings.modelName"
                type="text"
                class="input"
                placeholder="gemini-2.0-flash-exp"
              />
            </div>
          </div>
        </form>
      </div>
      
      <!-- FFMPEG Settings -->
      <div class="card p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">FFMPEG Settings</h2>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              FFMPEG Path
            </label>
            <input
              v-model="settings.ffmpegPath"
              type="text"
              class="input"
              placeholder="Path to FFMPEG executable"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              FFMPEG RAM Limit (GB)
            </label>
            <input
              v-model.number="settings.ffmpegRamLimit"
              type="number"
              min="1"
              max="32"
              class="input"
              placeholder="7.00"
            />
          </div>
        </div>
      </div>
      
      <!-- System Settings -->
      <div class="card p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">System Settings</h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Hide Logs
              </label>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Hide detailed logs from users
              </p>
            </div>
            <button
              @click="settings.hideLogs = !settings.hideLogs"
              :class="settings.hideLogs ? 'bg-primary-600' : 'bg-gray-200 dark:bg-dark-600'"
              class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors"
            >
              <span
                :class="settings.hideLogs ? 'translate-x-6' : 'translate-x-1'"
                class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
              />
            </button>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Default Credits for New Users
            </label>
            <input
              v-model.number="settings.defaultCredits"
              type="number"
              min="0"
              class="input"
              placeholder="10"
            />
          </div>
        </div>
      </div>
      
      <!-- Save Button -->
      <div class="flex justify-end">
        <button
          @click="saveSettings"
          :disabled="saving"
          class="btn-primary"
        >
          <span v-if="!saving">Save Settings</span>
          <span v-else class="flex items-center">
            <Loader2 class="animate-spin -ml-1 mr-2 h-4 w-4" />
            Saving...
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Eye, EyeOff, Loader2 } from 'lucide-vue-next'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

const toast = useToast()

const saving = ref(false)
const showGeminiKey = ref(false)
const showPexelsKey = ref(false)
const showPixabayKey = ref(false)

const settings = ref({
  llmProvider: 'gemini',
  language: 'en',
  geminiApiKey: '',
  pexelsApiKey: '',
  pixabayApiKey: '',
  baseUrl: '',
  modelName: 'gemini-2.0-flash-exp',
  ffmpegPath: '',
  ffmpegRamLimit: 7.0,
  hideLogs: false,
  defaultCredits: 10
})

const loadSettings = async () => {
  try {
    // TODO: Implement API call to load settings
    // For now, using mock data
    settings.value = {
      llmProvider: 'gemini',
      language: 'en',
      geminiApiKey: '••••••••••••••••',
      pexelsApiKey: '••••••••••••••••',
      pixabayApiKey: '••••••••••••••••',
      baseUrl: 'https://generativelanguage.googleapis.com',
      modelName: 'gemini-2.0-flash-exp',
      ffmpegPath: '/usr/bin/ffmpeg',
      ffmpegRamLimit: 7.0,
      hideLogs: false,
      defaultCredits: 10
    }
  } catch (error) {
    console.error('Failed to load settings:', error)
    toast.error('Failed to load settings')
  }
}

const saveSettings = async () => {
  saving.value = true
  try {
    // TODO: Implement API call to save settings
    await new Promise(resolve => setTimeout(resolve, 1000)) // Mock delay
    toast.success('Settings saved successfully')
  } catch (error) {
    console.error('Failed to save settings:', error)
    toast.error('Failed to save settings')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  loadSettings()
})
</script>
