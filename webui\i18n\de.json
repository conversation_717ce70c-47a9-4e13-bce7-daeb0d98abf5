{"Language": "German", "Translation": {"Video Script Settings": "**Drehbuch / Topic des Videos**", "Video Subject": "Worum soll es in dem Video gehen? (Geben Sie ein Keyword an, :red[Dank KI wird automatisch ein Drehbuch generieren])", "Script Language": "Welche Sprache soll zum Generieren von Drehbüchern  verwendet werden? :red[KI generiert anhand dieses Begriffs das Drehbuch]", "Generate Video Script and Keywords": "<PERSON><PERSON><PERSON> hier, um mithilfe von K<PERSON> ein [Video Drehbuch] und [Video Keywords] basierend auf dem **Keyword** zu generieren.", "Auto Detect": "Automatisch erkennen", "Video Script": "Drehbuch (Storybook) (:blue[① Optional, KI generiert  ② Die richtige Zeichensetzung hilft bei der Erstellung von Untertiteln])", "Generate Video Keywords": "<PERSON><PERSON><PERSON>, um KI zum Generieren zu verwenden [Video Keywords] basierend auf dem **Drehbuch**", "Please Enter the Video Subject": "Bitte geben Sie zu<PERSON>t das Drehbuch an", "Generating Video Script and Keywords": "KI generiert ein Drehbuch und Schlüsselwörter...", "Generating Video Keywords": "AI is generating video keywords...", "Video Keywords": "Video Schlüsselwörter (:blue[① Optional, KI generiert ② Verwende **, (Kommas)** zur Trennung der Wörter, in englischer Sprache])", "Video Settings": "**Video Einstellungen**", "Video Concat Mode": "Videoverkettungsmodus", "Random": "Zufällige Verkettung (empfohlen)", "Sequential": "Sequentielle Verkettung", "Video Transition Mode": "Video Übergangsmodus", "None": "<PERSON><PERSON>", "Shuffle": "Zufällige Übergänge", "FadeIn": "FadeIn", "FadeOut": "FadeOut", "SlideIn": "SlideIn", "SlideOut": "SlideOut", "Video Ratio": "Video-Seitenverhältnis", "Portrait": "Portrait 9:16", "Landscape": "Landschaft 16:9", "Clip Duration": "Maximale Dauer einzelner Videoclips in sekunden", "Number of Videos Generated Simultaneously": "<PERSON><PERSON><PERSON> der parallel generierten Videos", "Audio Settings": "**Audio Einstellungen**", "Speech Synthesis": "Sprachausgabe", "Speech Region": "Region(:red[Required，[Get Region](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API Key(:red[Required，[Get API Key](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "Lautstärke der Sprachausgabe", "Speech Rate": "Lesegeschwindigkeit (1,0 bedeutet 1x)", "Male": "<PERSON><PERSON><PERSON><PERSON>", "Female": "<PERSON><PERSON><PERSON>", "Background Music": "Hintergrundmusik", "No Background Music": "<PERSON>ne Hintergrundmusik", "Random Background Music": "Zufällig erzeugte Hintergrundmusik", "Custom Background Music": "Benutzerdefinierte Hintergrundmusik", "Custom Background Music File": "Bitte gib den Pfad zur Musikdatei an:", "Background Music Volume": "Lautstärke: (0.2 entspricht 20%, sollte nicht zu laut sein)", "Subtitle Settings": "**Untertitel-Einstellungen**", "Enable Subtitles": "Untertitel aktivieren (Wenn diese Option deaktiviert ist, werden die Einstellungen nicht genutzt)", "Font": "Schriftart des Untertitels", "Position": "Ausrichtung des Untertitels", "Top": "<PERSON><PERSON>", "Center": "<PERSON><PERSON><PERSON>", "Bottom": "<PERSON>ten (empfohlen)", "Custom": "Benutzerdefinierte Position (70, was 70% von oben bedeutet)", "Font Size": "Schriftgröße für Untertitel", "Font Color": "Schriftfarbe", "Stroke Color": "<PERSON><PERSON><PERSON>", "Stroke Width": "Breite der Untertitelkontur", "Generate Video": "Generiere Videos durch KI", "Video Script and Subject Cannot Both Be Empty": "Das Video-Thema und Drehbuch dürfen nicht beide leer sein", "Generating Video": "Video wird erstellt, bitte warten...", "Start Generating Video": "Beginne mit der Generierung", "Video Generation Completed": "Video erfolgreich generiert", "Video Generation Failed": "Video Generierung fehlgeschlagen", "You can download the generated video from the following links": "<PERSON>e können das generierte Video über die folgenden Links herunterladen", "Basic Settings": "**Grunde Instellungen**", "Pexels API Key": "Pexels API Key ([Get API Key](https://www.pexels.com/api/))", "Pixabay API Key": "Pixabay API Key ([Get API Key](https://pixabay.com/api/docs/#api_search_videos))", "Language": "Language", "LLM Provider": "LLM Provider", "API Key": "API Key (:red[Required])", "Base Url": "Base Url", "Model Name": "Model Name", "Please Enter the LLM API Key": "Please Enter the **LLM API Key**", "Please Enter the Pexels API Key": "Please Enter the **Pexels API Key**", "Please Enter the Pixabay API Key": "Please Enter the **Pixabay API Key**", "Get Help": "If you need help, or have any questions, you can join discord for help: https://harryai.cc", "Video Source": "Video Source", "TikTok": "TikTok (TikTok support is coming soon)", "Bilibili": "Bilibili (Bilibili support is coming soon)", "Xiaohongshu": "Xiaohongshu (Xiaohongshu support is coming soon)", "Local file": "Local file", "Play Voice": "Play Voice", "Voice Example": "This is an example text for testing speech synthesis", "Synthesizing Voice": "Synthesizing voice, please wait...", "TTS Provider": "Select the voice synthesis provider"}}