name: 🐛 Bug
description: 出现错误或未按预期工作
title: "请在此处填写标题"
labels:
  - bug

body:
  - type: markdown
    attributes:
      value: |
        **在提交此问题之前，请确保您已阅读以下文档：[Getting Started (英文)](https://github.com/harry0703/MoneyPrinterTurbo/blob/main/README-en.md#system-requirements-) 或 [快速开始 (中文)](https://github.com/harry0703/MoneyPrinterTurbo/blob/main/README.md#%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B-)。**

        **请填写以下信息：**
  - type: checkboxes
    attributes:
      label: 是否已存在类似问题？
      description: |
        请务必检查此问题是否已有用户反馈。
        
        在提交新问题前，使用 GitHub 的问题搜索框（包括已关闭的问题）或通过 Google、StackOverflow 等工具搜索，确认该问题是否重复。
        
        您可能已经可以找到解决问题的方法！
      options:
        - label: 我已搜索现有问题
          required: true
  - type: textarea
    attributes:
      label: 当前行为
      description: 描述您当前遇到的情况。
      placeholder: |
        MoneyPrinterTurbo 未按预期工作。当我执行某个操作时，视频未成功生成/程序报错了...
    validations:
      required: true
  - type: textarea
    attributes:
      label: 预期行为
      description: 描述您期望发生的情况。
      placeholder: |
        当我执行某个操作时，程序应当...
    validations:
      required: true
  - type: textarea
    attributes:
      label: 重现步骤
      description: 描述重现问题的步骤。描述的越详细，越有助于定位和修复问题。
    validations:
      required: true
  - type: textarea
    attributes:
      label: 堆栈追踪/日志
      description: |
        如果您有任何堆栈追踪或日志，请将它们粘贴在此处。（注意不要包含敏感信息）
    validations:
      required: true
  - type: input
    attributes:
      label: Python 版本
      description: 您遇到此问题时使用的 Python 版本。
      placeholder: v3.13.0, v3.10.0 等
    validations:
      required: true
  - type: input
    attributes:
      label: 操作系统
      description: 您使用 MoneyPrinterTurbo 遇到问题时的操作系统信息。
      placeholder: macOS 14.1, Windows 11 等
    validations:
      required: true
  - type: input
    attributes:
      label: MoneyPrinterTurbo 版本
      description: 您在哪个版本的 MoneyPrinterTurbo 中遇到了此问题？
      placeholder: v1.2.2 等
    validations:
      required: true
  - type: textarea
    attributes:
      label: 其他信息
      description: 您还有什么其他信息想补充吗？例如问题的截图或视频记录。
    validations:
      required: false