<template>
  <div class="min-h-screen bg-gray-50 dark:bg-dark-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900 mb-8">
        <AlertCircle class="h-12 w-12 text-red-600 dark:text-red-400" />
      </div>
      
      <h1 class="text-6xl font-bold text-gray-900 dark:text-white mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">Page Not Found</h2>
      <p class="text-gray-600 dark:text-gray-400 mb-8">
        The page you're looking for doesn't exist or has been moved.
      </p>
      
      <div class="space-y-4">
        <router-link
          to="/dashboard"
          class="btn-primary w-full"
        >
          <Home class="h-4 w-4 mr-2" />
          Go to Dashboard
        </router-link>
        
        <button
          @click="$router.go(-1)"
          class="btn-secondary w-full"
        >
          <ArrowLeft class="h-4 w-4 mr-2" />
          Go Back
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { AlertCircle, Home, ArrowLeft } from 'lucide-vue-next'
</script>
