@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-dark-700 dark:text-white dark:hover:bg-dark-600;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-800 dark:border-dark-600 dark:text-white;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 dark:bg-dark-800 dark:border-dark-700;
  }
  
  .sidebar-item {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors;
  }
  
  .sidebar-item-active {
    @apply sidebar-item bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300;
  }
  
  .sidebar-item-inactive {
    @apply sidebar-item text-gray-600 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-dark-700 dark:hover:text-white;
  }
}
