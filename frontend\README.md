# MoneyPrinter Turbo Frontend

Vue.js frontend application for MoneyPrinterTurbo - AI-powered video generation platform.

## Features

- 🔐 **Authentication System** - Google OAuth integration (placeholder)
- 👤 **User Dashboard** - Credit management and video generation
- 👨‍💼 **Admin Panel** - User management and system settings
- 🎨 **Modern UI** - Tailwind CSS with dark mode support
- 📱 **Responsive Design** - Works on all devices
- 🔄 **Real-time Updates** - Live status updates and notifications

## Tech Stack

- **Vue 3** - Progressive JavaScript framework
- **Vue Router** - Client-side routing
- **Pinia** - State management
- **Tailwind CSS** - Utility-first CSS framework
- **Vite** - Fast build tool
- **Axios** - HTTP client
- **Lucide Vue** - Beautiful icons

## Project Structure

```
frontend/
├── src/
│   ├── components/          # Reusable components
│   ├── layouts/            # Layout components
│   ├── views/              # Page components
│   │   ├── auth/           # Authentication pages
│   │   └── admin/          # Admin pages
│   ├── stores/             # Pinia stores
│   ├── services/           # API services
│   ├── router/             # Vue Router configuration
│   └── style.css           # Global styles
├── public/                 # Static assets
└── package.json           # Dependencies
```

## Installation

1. **Install dependencies:**
   ```bash
   cd frontend
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Build for production:**
   ```bash
   npm run build
   ```

## Configuration

### Environment Variables

Create a `.env` file in the frontend directory:

```env
# Google OAuth (to be implemented)
VUE_APP_GOOGLE_CLIENT_ID=your_google_client_id

# API Configuration
VUE_APP_API_BASE_URL=http://localhost:8080/api
```

### Backend Integration

The frontend is configured to proxy API requests to the backend:

- Development: `http://localhost:8080/api`
- Production: Configure in `vite.config.js`

## User Roles

### Regular User
- View credit balance
- Generate videos
- Download completed videos
- View generation history

### Admin User
- All user features
- User management
- Credit management
- System settings configuration
- View system statistics

## Development

### Running in Development

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

### Building for Production

```bash
npm run build
```

Built files will be in the `dist/` directory.

### Linting

```bash
npm run lint
```

## API Integration

The frontend communicates with the MoneyPrinterTurbo backend through REST APIs:

- **Authentication**: `/api/auth/*`
- **User Management**: `/api/users/*`
- **Video Generation**: `/api/videos/*`
- **Admin Operations**: `/api/admin/*`

## Authentication Flow

1. User clicks "Continue with Google"
2. Google OAuth popup (to be implemented)
3. Backend validates Google token
4. JWT token returned and stored
5. User redirected to dashboard

## Deployment

### Manual Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy the `dist/` folder to your web server

3. Configure your web server to serve the SPA:
   - Redirect all routes to `index.html`
   - Set proper MIME types for assets

### Docker Deployment

```dockerfile
FROM node:18-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of MoneyPrinterTurbo and follows the same license terms.
