<template>
  <div class="p-8">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
      <p class="mt-2 text-gray-600 dark:text-gray-400">
        Overview of system statistics and user activity
      </p>
    </div>
    
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Users class="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalUsers }}</p>
          </div>
        </div>
      </div>
      
      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <Video class="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Videos Generated</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalVideos }}</p>
          </div>
        </div>
      </div>
      
      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <Coins class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Distributed</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.totalCredits }}</p>
          </div>
        </div>
      </div>
      
      <div class="card p-6">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
            <TrendingUp class="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Today</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.activeToday }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Charts and Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Activity -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Recent Activity</h2>
        <div class="space-y-4">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-dark-700 rounded-lg"
          >
            <div class="flex items-center">
              <div class="h-8 w-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                <User class="h-4 w-4 text-primary-600 dark:text-primary-400" />
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ activity.user }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.action }}</p>
              </div>
            </div>
            <span class="text-xs text-gray-500 dark:text-gray-400">{{ activity.time }}</span>
          </div>
        </div>
      </div>
      
      <!-- System Status -->
      <div class="card p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">System Status</h2>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">API Status</span>
            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-full">
              Operational
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Database</span>
            <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-full">
              Connected
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Storage</span>
            <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 rounded-full">
              75% Used
            </span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Queue</span>
            <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-full">
              {{ stats.queueSize }} jobs
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Users, Video, Coins, TrendingUp, User } from 'lucide-vue-next'
import api from '@/services/api'

const stats = ref({
  totalUsers: 0,
  totalVideos: 0,
  totalCredits: 0,
  activeToday: 0,
  queueSize: 0
})

const recentActivity = ref([
  {
    id: 1,
    user: '<EMAIL>',
    action: 'Generated video',
    time: '2 minutes ago'
  },
  {
    id: 2,
    user: '<EMAIL>',
    action: 'Signed up',
    time: '5 minutes ago'
  },
  {
    id: 3,
    user: '<EMAIL>',
    action: 'Generated video',
    time: '10 minutes ago'
  }
])

const fetchStats = async () => {
  try {
    // TODO: Implement API call to fetch admin stats
    // For now, using mock data
    stats.value = {
      totalUsers: 156,
      totalVideos: 1247,
      totalCredits: 5000,
      activeToday: 23,
      queueSize: 5
    }
  } catch (error) {
    console.error('Failed to fetch admin stats:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>
