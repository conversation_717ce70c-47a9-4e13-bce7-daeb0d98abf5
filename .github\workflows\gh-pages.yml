name: gp-pages
on:
  push:
    branches:
      - main
    paths:
      - 'sites/**'

jobs:
  build-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set-up Node
        uses: actions/setup-node@v1
        with:
          node-version: "18.15.0"
      - name: Install pnpm
        run: npm install -g pnpm
      - name: Install dependencies
        run: pnpm i
        working-directory: ./sites
      - name: Build gh-pages
        run: pnpm docs:build
        working-directory: ./sites
      - name: Deploy to gh-pages
        uses: crazy-max/ghaction-github-pages@v1
        with:
          target_branch: gh-pages
          build_dir: sites/docs/.vuepress/dist
        env:
          GITHUB_TOKEN: ${{ secrets.MY_TOKEN }}
