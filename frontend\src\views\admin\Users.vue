<template>
  <div class="p-8">
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">User Management</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          Manage users and their credits
        </p>
      </div>
      
      <button
        @click="showAddCreditsModal = true"
        class="btn-primary"
      >
        <Plus class="h-4 w-4 mr-2" />
        Add Credits
      </button>
    </div>
    
    <!-- Search and Filters -->
    <div class="card p-6 mb-6">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search users..."
            class="input"
          />
        </div>
        <select v-model="roleFilter" class="input sm:w-48">
          <option value="">All Roles</option>
          <option value="user">User</option>
          <option value="admin">Admin</option>
        </select>
      </div>
    </div>
    
    <!-- Users Table -->
    <div class="card overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-dark-700">
          <thead class="bg-gray-50 dark:bg-dark-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                User
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Role
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Credits
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Videos
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Last Active
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-dark-700">
            <tr
              v-for="user in filteredUsers"
              :key="user.id"
              class="hover:bg-gray-50 dark:hover:bg-dark-700"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                    <User class="h-5 w-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ user.name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="user.role === 'admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'">
                  {{ user.role }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ user.credits }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ user.videosGenerated }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ user.lastActive }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="editUser(user)"
                    class="text-primary-600 hover:text-primary-900 dark:text-primary-400"
                  >
                    <Edit class="h-4 w-4" />
                  </button>
                  <button
                    @click="addCreditsToUser(user)"
                    class="text-green-600 hover:text-green-900 dark:text-green-400"
                  >
                    <Plus class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- Add Credits Modal -->
    <div
      v-if="showAddCreditsModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white dark:bg-dark-800 rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Add Credits</h3>
        
        <form @submit.prevent="submitAddCredits" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select User
            </label>
            <select v-model="creditForm.userId" class="input" required>
              <option value="">Choose a user...</option>
              <option v-for="user in users" :key="user.id" :value="user.id">
                {{ user.name }} ({{ user.email }})
              </option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Credits Amount
            </label>
            <input
              v-model.number="creditForm.amount"
              type="number"
              min="1"
              required
              class="input"
              placeholder="Enter credits amount"
            />
          </div>
          
          <div class="flex justify-end space-x-3">
            <button
              type="button"
              @click="showAddCreditsModal = false"
              class="btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="btn-primary"
            >
              <span v-if="!loading">Add Credits</span>
              <span v-else class="flex items-center">
                <Loader2 class="animate-spin -ml-1 mr-2 h-4 w-4" />
                Adding...
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Plus, User, Edit, Loader2 } from 'lucide-vue-next'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

const toast = useToast()

const users = ref([])
const searchQuery = ref('')
const roleFilter = ref('')
const loading = ref(false)
const showAddCreditsModal = ref(false)

const creditForm = ref({
  userId: '',
  amount: 0
})

const filteredUsers = computed(() => {
  return users.value.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesRole = !roleFilter.value || user.role === roleFilter.value
    return matchesSearch && matchesRole
  })
})

const fetchUsers = async () => {
  try {
    // TODO: Implement API call to fetch users
    // For now, using mock data
    users.value = [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
        credits: 50,
        videosGenerated: 12,
        lastActive: '2 hours ago'
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'user',
        credits: 25,
        videosGenerated: 8,
        lastActive: '1 day ago'
      },
      {
        id: 3,
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        credits: 1000,
        videosGenerated: 0,
        lastActive: '5 minutes ago'
      }
    ]
  } catch (error) {
    console.error('Failed to fetch users:', error)
  }
}

const editUser = (user) => {
  // TODO: Implement user editing
  toast.info('User editing will be implemented')
}

const addCreditsToUser = (user) => {
  creditForm.value.userId = user.id
  showAddCreditsModal.value = true
}

const submitAddCredits = async () => {
  loading.value = true
  try {
    // TODO: Implement API call to add credits
    const user = users.value.find(u => u.id === creditForm.value.userId)
    if (user) {
      user.credits += creditForm.value.amount
      toast.success(`Added ${creditForm.value.amount} credits to ${user.name}`)
    }
    
    showAddCreditsModal.value = false
    creditForm.value = { userId: '', amount: 0 }
  } catch (error) {
    toast.error('Failed to add credits')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchUsers()
})
</script>
