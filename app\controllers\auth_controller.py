from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import RedirectResponse
from pydantic import BaseModel
from google.oauth2 import id_token
from google.auth.transport import requests
import os
import jwt
import datetime
from typing import Optional
import httpx

router = APIRouter(prefix="/api/auth", tags=["authentication"])

# Pydantic models
class LoginRequest(BaseModel):
    email: str
    password: str

class GoogleLoginRequest(BaseModel):
    token: str

class UserResponse(BaseModel):
    id: str
    name: str
    email: str
    role: str
    credits: int
    picture: Optional[str] = None

class AuthResponse(BaseModel):
    token: str
    user: UserResponse

# Google OAuth configuration
GOOGLE_CLIENT_ID = "946728822396-h7uk2lag3t0ra75iliao1peq3732n3v3.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
JWT_SECRET = os.getenv('JWT_SECRET', 'your-secret-key-here')

# Mock user database (replace with real database)
users_db = {}

def generate_jwt_token(user_data):
    """Generate JWT token for user"""
    payload = {
        'user_id': user_data['id'],
        'email': user_data['email'],
        'role': user_data['role'],
        'exp': datetime.datetime.utcnow() + datetime.timedelta(days=7)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def verify_jwt_token(token):
    """Verify JWT token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def get_current_user_from_token(authorization: str = Depends(lambda: None)):
    """Get current user from JWT token"""
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No token provided"
        )

    if authorization.startswith('Bearer '):
        token = authorization[7:]
    else:
        token = authorization

    payload = verify_jwt_token(token)
    if not payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

    return payload

@router.post("/login", response_model=AuthResponse)
async def login(login_data: LoginRequest):
    """Regular email/password login"""
    email = login_data.email
    password = login_data.password

    # Mock authentication (replace with real authentication)
    if email == '<EMAIL>' and password == 'password':
        user_data = {
            'id': '1',
            'name': 'Admin User',
            'email': '<EMAIL>',
            'role': 'admin',
            'credits': 1000
        }
    elif email == '<EMAIL>' and password == 'password':
        user_data = {
            'id': '2',
            'name': 'Regular User',
            'email': '<EMAIL>',
            'role': 'user',
            'credits': 100
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )

    token = generate_jwt_token(user_data)
    users_db[user_data['id']] = user_data

    return AuthResponse(
        token=token,
        user=UserResponse(**user_data)
    )

@router.post("/google", response_model=AuthResponse)
async def google_login(google_data: GoogleLoginRequest):
    """Google OAuth login"""
    google_token = google_data.token

    try:
        # Verify Google token
        idinfo = id_token.verify_oauth2_token(
            google_token,
            requests.Request(),
            GOOGLE_CLIENT_ID
        )

        # Get user info from Google token
        google_user_id = idinfo['sub']
        email = idinfo['email']
        name = idinfo['name']
        picture = idinfo.get('picture', '')

        # Check if user exists or create new user
        user_data = {
            'id': google_user_id,
            'name': name,
            'email': email,
            'picture': picture,
            'role': 'admin' if 'admin' in email else 'user',
            'credits': 100
        }

        # Store user in database
        users_db[google_user_id] = user_data

        # Generate JWT token
        token = generate_jwt_token(user_data)

        return AuthResponse(
            token=token,
            user=UserResponse(**user_data)
        )

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid Google token"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user(current_user: dict = Depends(get_current_user_from_token)):
    """Get current user info"""
    user_id = current_user['user_id']
    user_data = users_db.get(user_id)

    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return UserResponse(**user_data)

@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user_from_token)):
    """Logout user"""
    # In a real app, you might want to blacklist the token
    return {"message": "Logged out successfully"}

# Google OAuth authorization URL
@router.get("/google/authorize")
async def google_authorize():
    """Redirect to Google OAuth authorization"""
    from urllib.parse import urlencode

    params = {
        'client_id': GOOGLE_CLIENT_ID,
        'redirect_uri': 'http://127.0.0.1:3000/authorize',
        'scope': 'openid email profile',
        'response_type': 'code',
        'access_type': 'offline',
        'prompt': 'consent'
    }

    auth_url = f"https://accounts.google.com/o/oauth2/auth?{urlencode(params)}"
    return RedirectResponse(url=auth_url)

@router.get("/google/callback")
async def google_callback(code: str = None):
    """Handle Google OAuth callback"""
    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No authorization code provided"
        )

    try:
        # Exchange authorization code for access token
        token_url = 'https://oauth2.googleapis.com/token'
        token_data = {
            'client_id': GOOGLE_CLIENT_ID,
            'client_secret': GOOGLE_CLIENT_SECRET,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': 'http://127.0.0.1:3000/authorize'
        }

        async with httpx.AsyncClient() as client:
            token_response = await client.post(token_url, data=token_data)
            token_json = token_response.json()

            if 'access_token' not in token_json:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get access token"
                )

            # Get user info from Google
            user_info_url = f"https://www.googleapis.com/oauth2/v2/userinfo?access_token={token_json['access_token']}"
            user_response = await client.get(user_info_url)
            user_info = user_response.json()

        # Create user data
        user_data = {
            'id': user_info['id'],
            'name': user_info['name'],
            'email': user_info['email'],
            'picture': user_info.get('picture', ''),
            'role': 'admin' if 'admin' in user_info['email'] else 'user',
            'credits': 100
        }

        # Store user in database
        users_db[user_info['id']] = user_data

        # Generate JWT token
        jwt_token = generate_jwt_token(user_data)

        # Redirect to frontend with token
        frontend_url = f"http://127.0.0.1:3000/authorize?access_token={token_json['access_token']}&jwt_token={jwt_token}"
        return RedirectResponse(url=frontend_url)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
