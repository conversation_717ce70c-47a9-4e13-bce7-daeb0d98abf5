// Google OAuth service
export class GoogleAuthService {
  constructor() {
    this.clientId = import.meta.env.VUE_APP_GOOGLE_CLIENT_ID || ''
    this.isInitialized = false
  }

  async initialize() {
    if (this.isInitialized) return

    return new Promise((resolve, reject) => {
      if (typeof window.google !== 'undefined') {
        this.isInitialized = true
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://accounts.google.com/gsi/client'
      script.async = true
      script.defer = true
      script.onload = () => {
        window.google.accounts.id.initialize({
          client_id: this.clientId,
          callback: this.handleCredentialResponse.bind(this),
          auto_select: false,
          cancel_on_tap_outside: false
        })
        this.isInitialized = true
        resolve()
      }
      script.onerror = () => reject(new Error('Failed to load Google OAuth script'))
      document.head.appendChild(script)
    })
  }

  async signIn() {
    try {
      await this.initialize()

      return new Promise((resolve, reject) => {
        // Try One Tap first
        window.google.accounts.id.prompt((notification) => {
          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
            // Fallback to OAuth popup
            const client = window.google.accounts.oauth2.initTokenClient({
              client_id: this.clientId,
              scope: 'email profile openid',
              callback: async (response) => {
                if (response.access_token) {
                  try {
                    const userInfo = await this.getUserInfo(response.access_token)
                    resolve({
                      token: response.access_token,
                      user: userInfo
                    })
                  } catch (error) {
                    reject(error)
                  }
                } else {
                  reject(new Error('Failed to get access token'))
                }
              }
            })
            client.requestAccessToken()
          }
        })

        // Set up credential response handler for One Tap
        this.onCredentialResponse = resolve
      })
    } catch (error) {
      throw new Error('Failed to initialize Google Auth: ' + error.message)
    }
  }

  async getUserInfo(accessToken) {
    const response = await fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${accessToken}`)
    if (!response.ok) {
      throw new Error('Failed to get user info from Google')
    }
    const userInfo = await response.json()

    return {
      id: userInfo.id,
      name: userInfo.name,
      email: userInfo.email,
      picture: userInfo.picture,
      role: 'user' // Default role, backend should determine actual role
    }
  }

  handleCredentialResponse(response) {
    try {
      // Decode JWT token to get user info
      const credential = response.credential
      const payload = JSON.parse(atob(credential.split('.')[1]))

      const result = {
        token: credential,
        user: {
          id: payload.sub,
          name: payload.name,
          email: payload.email,
          picture: payload.picture,
          role: 'user'
        }
      }

      if (this.onCredentialResponse) {
        this.onCredentialResponse(result)
      }
    } catch (error) {
      console.error('Error handling credential response:', error)
    }
  }

  async signOut() {
    if (window.google && window.google.accounts) {
      window.google.accounts.id.disableAutoSelect()
    }
  }
}

export const googleAuth = new GoogleAuthService()
