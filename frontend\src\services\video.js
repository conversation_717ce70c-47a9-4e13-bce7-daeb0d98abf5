import api from './api'

export class VideoService {
  /**
   * Generate a new video
   * @param {Object} params - Video generation parameters
   * @param {string} params.subject - Video subject/topic
   * @param {number} params.duration - Video duration in seconds
   * @param {string} params.voice - TTS voice to use
   * @param {number} params.count - Number of videos to generate
   * @returns {Promise} API response
   */
  static async generateVideo(params) {
    return api.post('/videos/generate', params)
  }

  /**
   * Get user's video history
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise} API response
   */
  static async getVideoHistory(options = {}) {
    const params = new URLSearchParams({
      page: options.page || 1,
      limit: options.limit || 10
    })
    return api.get(`/videos/history?${params}`)
  }

  /**
   * Get video status
   * @param {string} videoId - Video ID
   * @returns {Promise} API response
   */
  static async getVideoStatus(videoId) {
    return api.get(`/videos/${videoId}/status`)
  }

  /**
   * Download video
   * @param {string} videoId - Video ID
   * @returns {Promise} API response with blob
   */
  static async downloadVideo(videoId) {
    return api.get(`/videos/${videoId}/download`, {
      responseType: 'blob'
    })
  }

  /**
   * Delete video
   * @param {string} videoId - Video ID
   * @returns {Promise} API response
   */
  static async deleteVideo(videoId) {
    return api.delete(`/videos/${videoId}`)
  }
}

export default VideoService
