import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useUserStore = defineStore('user', () => {
  const credits = ref(0)
  const usage = ref({
    videosGenerated: 0,
    creditsUsed: 0,
    lastActivity: null
  })
  const loading = ref(false)

  const hasCredits = computed(() => credits.value > 0)

  const fetchUserData = async () => {
    loading.value = true
    try {
      const response = await api.get('/user/profile')
      credits.value = response.data.credits
      usage.value = response.data.usage
    } catch (error) {
      console.error('Failed to fetch user data:', error)
    } finally {
      loading.value = false
    }
  }

  const updateCredits = (amount) => {
    credits.value = amount
  }

  const deductCredits = (amount) => {
    credits.value = Math.max(0, credits.value - amount)
    usage.value.creditsUsed += amount
  }

  const addCredits = (amount) => {
    credits.value += amount
  }

  return {
    credits,
    usage,
    loading,
    hasCredits,
    fetchUserData,
    updateCredits,
    deductCredits,
    addCredits
  }
})
