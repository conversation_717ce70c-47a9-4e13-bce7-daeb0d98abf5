import api from './api'

export class AdminService {
  /**
   * Get admin dashboard statistics
   * @returns {Promise} API response
   */
  static async getDashboardStats() {
    return api.get('/admin/stats')
  }

  /**
   * Get all users
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.search - Search query
   * @param {string} options.role - Role filter
   * @returns {Promise} API response
   */
  static async getUsers(options = {}) {
    const params = new URLSearchParams()
    if (options.page) params.append('page', options.page)
    if (options.limit) params.append('limit', options.limit)
    if (options.search) params.append('search', options.search)
    if (options.role) params.append('role', options.role)
    
    return api.get(`/admin/users?${params}`)
  }

  /**
   * Update user
   * @param {string} userId - User ID
   * @param {Object} userData - User data to update
   * @returns {Promise} API response
   */
  static async updateUser(userId, userData) {
    return api.put(`/admin/users/${userId}`, userData)
  }

  /**
   * Add credits to user
   * @param {string} userId - User ID
   * @param {number} amount - Credits amount to add
   * @returns {Promise} API response
   */
  static async addCredits(userId, amount) {
    return api.post(`/admin/users/${userId}/credits`, { amount })
  }

  /**
   * Delete user
   * @param {string} userId - User ID
   * @returns {Promise} API response
   */
  static async deleteUser(userId) {
    return api.delete(`/admin/users/${userId}`)
  }

  /**
   * Get system settings
   * @returns {Promise} API response
   */
  static async getSettings() {
    return api.get('/admin/settings')
  }

  /**
   * Update system settings
   * @param {Object} settings - Settings to update
   * @returns {Promise} API response
   */
  static async updateSettings(settings) {
    return api.put('/admin/settings', settings)
  }

  /**
   * Get recent activity
   * @param {number} limit - Number of activities to fetch
   * @returns {Promise} API response
   */
  static async getRecentActivity(limit = 10) {
    return api.get(`/admin/activity?limit=${limit}`)
  }

  /**
   * Get system status
   * @returns {Promise} API response
   */
  static async getSystemStatus() {
    return api.get('/admin/system/status')
  }
}

export default AdminService
