<template>
  <MainLayout>
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="px-4 py-6 sm:px-0">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">
          Create amazing videos with AI
        </p>
      </div>
      
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="card p-6">
          <div class="flex items-center">
            <div class="p-2 bg-primary-100 dark:bg-primary-900 rounded-lg">
              <Coins class="h-6 w-6 text-primary-600 dark:text-primary-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Available Credits</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ userStore.credits }}</p>
            </div>
          </div>
        </div>
        
        <div class="card p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
              <Video class="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Videos Generated</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ userStore.usage.videosGenerated }}</p>
            </div>
          </div>
        </div>
        
        <div class="card p-6">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
              <TrendingUp class="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Credits Used</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ userStore.usage.creditsUsed }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Video Generation Form -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="card p-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Create New Video</h2>
          
          <form @submit.prevent="generateVideo" class="space-y-6">
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Video Subject
              </label>
              <input
                id="subject"
                v-model="videoForm.subject"
                type="text"
                required
                class="input"
                placeholder="Enter your video topic..."
              />
            </div>
            
            <div>
              <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Duration (seconds)
              </label>
              <select id="duration" v-model="videoForm.duration" class="input">
                <option value="30">30 seconds</option>
                <option value="60">1 minute</option>
                <option value="120">2 minutes</option>
                <option value="300">5 minutes</option>
              </select>
            </div>
            
            <div>
              <label for="voice" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Voice
              </label>
              <select id="voice" v-model="videoForm.voice" class="input">
                <option value="alloy">Alloy</option>
                <option value="echo">Echo</option>
                <option value="fable">Fable</option>
                <option value="onyx">Onyx</option>
                <option value="nova">Nova</option>
                <option value="shimmer">Shimmer</option>
              </select>
            </div>
            
            <div>
              <label for="count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Number of Videos
              </label>
              <select id="count" v-model="videoForm.count" class="input">
                <option value="1">1 video</option>
                <option value="2">2 videos</option>
                <option value="3">3 videos</option>
                <option value="5">5 videos</option>
              </select>
            </div>
            
            <button
              type="submit"
              :disabled="generating || !userStore.hasCredits"
              class="btn-primary w-full"
            >
              <span v-if="!generating">Generate Video</span>
              <span v-else class="flex items-center justify-center">
                <Loader2 class="animate-spin -ml-1 mr-2 h-4 w-4" />
                Generating...
              </span>
            </button>
            
            <p v-if="!userStore.hasCredits" class="text-sm text-red-600 dark:text-red-400">
              You don't have enough credits to generate videos.
            </p>
          </form>
        </div>
        
        <!-- Recent Videos -->
        <div class="card p-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Recent Videos</h2>
          
          <div v-if="recentVideos.length === 0" class="text-center py-8">
            <Video class="mx-auto h-12 w-12 text-gray-400" />
            <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No videos generated yet</p>
          </div>
          
          <div v-else class="space-y-4">
            <div
              v-for="video in recentVideos"
              :key="video.id"
              class="flex items-center justify-between p-4 bg-gray-50 dark:bg-dark-700 rounded-lg"
            >
              <div>
                <p class="font-medium text-gray-900 dark:text-white">{{ video.subject }}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ video.createdAt }}</p>
              </div>
              <div class="flex items-center space-x-2">
                <span class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="getStatusClass(video.status)">
                  {{ video.status }}
                </span>
                <button
                  v-if="video.status === 'completed'"
                  @click="downloadVideo(video)"
                  class="text-primary-600 hover:text-primary-700 dark:text-primary-400"
                >
                  <Download class="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Video, Coins, TrendingUp, Loader2, Download } from 'lucide-vue-next'
import MainLayout from '@/layouts/MainLayout.vue'
import { useUserStore } from '@/stores/user'
import { useToast } from 'vue-toastification'
import api from '@/services/api'

const userStore = useUserStore()
const toast = useToast()

const generating = ref(false)
const recentVideos = ref([])

const videoForm = ref({
  subject: '',
  duration: 60,
  voice: 'alloy',
  count: 1
})

const generateVideo = async () => {
  if (!userStore.hasCredits) {
    toast.error('Insufficient credits')
    return
  }
  
  generating.value = true
  try {
    const response = await api.post('/videos/generate', videoForm.value)
    toast.success('Video generation started!')
    
    // Add to recent videos
    recentVideos.value.unshift({
      id: response.data.id,
      subject: videoForm.value.subject,
      status: 'processing',
      createdAt: new Date().toLocaleDateString()
    })
    
    // Reset form
    videoForm.value.subject = ''
    
    // Deduct credits
    userStore.deductCredits(videoForm.value.count)
    
  } catch (error) {
    toast.error('Failed to generate video')
  } finally {
    generating.value = false
  }
}

const downloadVideo = (video) => {
  // TODO: Implement video download
  toast.info('Download functionality will be implemented')
}

const getStatusClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
    case 'failed':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
  }
}

onMounted(() => {
  // Load recent videos
  // TODO: Implement API call to fetch user's videos
})
</script>
